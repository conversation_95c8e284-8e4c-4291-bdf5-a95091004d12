import openai
import tiktoken
import time
enc = tiktoken.get_encoding("cl100k_base")
assert enc.decode(enc.encode("hello world")) == "hello world"
enc = tiktoken.encoding_for_model("gpt-4")

# Updated LLM configuration
openai_api_key_name = 'sk-YyiBg6DSn1Fc2KBNU6ZYtw'
openai_base_url = 'http://47.117.124.84:4033/v1'

# Initialize OpenAI clients for different endpoints
custom_client = openai.OpenAI(
    api_key=openai_api_key_name,
    base_url=openai_base_url
)

# For original OpenAI API (if needed)
openai_client = openai.OpenAI(
    api_key=openai_api_key_name
)

def get_model_for_framework(framework, agent_type='central', local_model_choice='gemma-3-27b'):
    """
    Get the appropriate model for a given framework and agent type.

    Args:
        framework: 'CMAS', 'DMAS', 'HMAS-1', 'HMAS-2'
        agent_type: 'central' or 'local' (for hierarchical frameworks)
        local_model_choice: 'gemma-3-27b' or 'qwen3-30b-a3b-mlx' (for DMAS and local agents)

    Returns:
        model_name: The appropriate model name
    """
    if framework == 'CMAS':
        return 'qwen3-235b-a22b'
    elif framework == 'DMAS':
        return local_model_choice
    elif framework in ['HMAS-1', 'HMAS-2']:
        if agent_type == 'central':
            return 'qwen3-235b-a22b'
        else:  # local agent
            return local_model_choice
    else:
        raise ValueError(f'Invalid framework: {framework}')

def GPT_response(messages, model_name):
  token_num_count = 0
  for item in messages:
    token_num_count += len(enc.encode(item["content"]))

  # Support both old GPT models and new custom models
  supported_models = [
      'qwen3-235b-a22b', 'gemma-3-27b', 'qwen3-30b-a3b-mlx'
  ]

  if model_name in supported_models:
    #print(f'-------------------Model name: {model_name}-------------------')

    # Choose the appropriate client based on model type
    if model_name.startswith('gpt-'):
        # Use original OpenAI API for GPT models
        client = openai_client
    else:
        # Use custom endpoint for new models
        client = custom_client

    try:
      result = client.chat.completions.create(
        model=model_name,
        messages=messages,
        temperature=0.0,
        top_p=1,
        frequency_penalty=0,
        presence_penalty=0
      )
    except Exception as e:
      print(f'First attempt failed: {str(e)}')
      try:
        result = client.chat.completions.create(
          model=model_name,
          messages=messages,
          temperature=0.0,
          top_p=1,
          frequency_penalty=0,
          presence_penalty=0
        )
      except Exception as e:
        print(f'Second attempt failed: {str(e)}')
        try:
          print(f'{model_name} Waiting 60 seconds for API query')
          time.sleep(60)
          result = client.chat.completions.create(
            model=model_name,
            messages=messages,
            temperature=0.0,
            top_p=1,
            frequency_penalty=0,
            presence_penalty=0
          )
        except Exception as e:
          print(f'Final attempt failed: {str(e)}')
          return 'Out of tokens', token_num_count

    # Extract response content using the new API structure
    response_content = result.choices[0].message.content
    token_num_count += len(enc.encode(response_content))
    print(f'Token_num_count: {token_num_count}')
    return response_content, token_num_count

  else:
    raise ValueError(f'Invalid model name: {model_name}')
