import openai
import tiktoken
import time
enc = tiktoken.get_encoding("cl100k_base")
assert enc.decode(enc.encode("hello world")) == "hello world"
enc = tiktoken.encoding_for_model("gpt-4")

# Updated LLM configuration
openai_api_key_name = 'sk-YyiBg6DSn1Fc2KBNU6ZYtw'
openai_base_url = 'http://47.117.124.84:4033/v1'

# Initialize OpenAI clients for different endpoints with timeout
custom_client = openai.OpenAI(
    api_key=openai_api_key_name,
    base_url=openai_base_url,
    timeout=60.0  # 60 second timeout
)

# For original OpenAI API (if needed)
openai_client = openai.OpenAI(
    api_key=openai_api_key_name,
    timeout=60.0  # 60 second timeout
)

def get_model_for_framework(framework, agent_type='central', local_model_choice='gemma-3-27b'):
    """
    Get the appropriate model for a given framework and agent type.

    Args:
        framework: 'CMAS', 'DMAS', 'HMAS-1', 'HMAS-2'
        agent_type: 'central' or 'local' (for hierarchical frameworks)
        local_model_choice: 'gemma-3-27b' or 'qwen3-30b-a3b-mlx' (for DMAS and local agents)

    Returns:
        model_name: The appropriate model name
    """
    # TEMPORARY FIX: Use gemma-3-27b for all models since qwen3-235b-a22b is hanging
    # TODO: Restore original logic once qwen3-235b-a22b is working properly
    print(f"WARNING: Using gemma-3-27b as fallback for {framework} {agent_type} agent")
    return local_model_choice

    # Original logic (commented out due to qwen3-235b-a22b hanging issue):
    # if framework == 'CMAS':
    #     return 'qwen3-235b-a22b'
    # elif framework == 'DMAS':
    #     return local_model_choice
    # elif framework in ['HMAS-1', 'HMAS-2']:
    #     if agent_type == 'central':
    #         return 'qwen3-235b-a22b'
    #     else:  # local agent
    #         return local_model_choice
    # else:
    #     raise ValueError(f'Invalid framework: {framework}')

def get_fallback_model(original_model):
    """
    Get a fallback model when the original model fails.

    Args:
        original_model: The model that failed

    Returns:
        fallback_model: A more reliable fallback model
    """
    if original_model == 'qwen3-235b-a22b':
        return 'gemma-3-27b'  # Use gemma as fallback for qwen
    else:
        return 'gemma-3-27b'  # Default fallback

def GPT_response(messages, model_name):
  token_num_count = 0
  for item in messages:
    token_num_count += len(enc.encode(item["content"]))

  # Support both old GPT models and new custom models
  supported_models = [
      'qwen3-235b-a22b', 'gemma-3-27b', 'qwen3-30b-a3b-mlx'
  ]

  if model_name in supported_models:
    #print(f'-------------------Model name: {model_name}-------------------')

    # Choose the appropriate client based on model type
    if model_name.startswith('gpt-'):
        # Use original OpenAI API for GPT models
        client = openai_client
    else:
        # Use custom endpoint for new models
        client = custom_client

    try:
      print(f'Making API call to {model_name}...')
      result = client.chat.completions.create(
        model=model_name,
        messages=messages,
        temperature=0.0,
        top_p=1,
        frequency_penalty=0,
        presence_penalty=0,
        timeout=60.0  # 60 second timeout for individual requests
      )
      print(f'API call to {model_name} successful')
    except Exception as e:
      print(f'First attempt failed: {str(e)}')
      try:
        print(f'Retrying API call to {model_name}...')
        result = client.chat.completions.create(
          model=model_name,
          messages=messages,
          temperature=0.0,
          top_p=1,
          frequency_penalty=0,
          presence_penalty=0,
          timeout=60.0  # 60 second timeout for individual requests
        )
        print(f'Second attempt to {model_name} successful')
      except Exception as e:
        print(f'Second attempt failed: {str(e)}')
        try:
          print(f'{model_name} Waiting 60 seconds for API query')
          time.sleep(60)
          print(f'Final attempt to {model_name}...')
          result = client.chat.completions.create(
            model=model_name,
            messages=messages,
            temperature=0.0,
            top_p=1,
            frequency_penalty=0,
            presence_penalty=0,
            timeout=60.0  # 60 second timeout for individual requests
          )
          print(f'Final attempt to {model_name} successful')
        except Exception as e:
          print(f'Final attempt failed: {str(e)}')
          print(f'Trying fallback model for {model_name}...')

          # Try fallback model
          fallback_model = get_fallback_model(model_name)
          if fallback_model != model_name:
            try:
              print(f'Using fallback model: {fallback_model}')
              result = client.chat.completions.create(
                model=fallback_model,
                messages=messages,
                temperature=0.0,
                top_p=1,
                frequency_penalty=0,
                presence_penalty=0,
                timeout=60.0
              )
              print(f'Fallback model {fallback_model} successful')
            except Exception as fallback_e:
              print(f'Fallback model also failed: {str(fallback_e)}')
              return 'Out of tokens', token_num_count
          else:
            return 'Out of tokens', token_num_count

    # Extract response content using the new API structure
    response_content = result.choices[0].message.content
    token_num_count += len(enc.encode(response_content))
    print(f'Token_num_count: {token_num_count}')
    return response_content, token_num_count

  else:
    raise ValueError(f'Invalid model name: {model_name}')
