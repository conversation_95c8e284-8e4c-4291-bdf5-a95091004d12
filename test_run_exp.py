#!/usr/bin/env python3

from LLM import *
from prompt_env2 import *
from env2_create import *
from sre_constants import error
import random
import os
import json
import re
import copy
import numpy as np
import shutil
import time

print("All imports successful!")

# Configuration for model selection
cen_decen_framework = 'HMAS-2'  # Options: 'CMAS', 'DMAS', 'HMAS-1', 'HMAS-2'
local_model_choice = 'gemma-3-27b'  # Options: 'gemma-3-27b', 'qwen3-30b-a3b-mlx'

print(f'-------------------Framework: {cen_decen_framework}, Local Model: {local_model_choice}-------------------')

# Test the first iteration only
pg_row_num, pg_column_num = 2, 2
iteration_num = 0
query_time_limit = 30

print('-------###-------###-------###-------')
print(f'Row num is: {pg_row_num}, Column num is: {pg_column_num}, Iteration num is: {iteration_num}\n\n')
print(f'query_time_limit: {query_time_limit}')

# Test path creation
Code_dir_path = 'path_to_multi-agent-framework/multi-agent-framework/' # Put the current code directory path here
Saving_path = Code_dir_path + 'Env2_WorkpieceNet2'

print(f"About to call run_exp function...")

try:
    # Import the run_exp function from env2-box-arrange
    import sys
    sys.path.append('.')

    # Import the run_exp function
    import importlib.util
    spec = importlib.util.spec_from_file_location("env2_box_arrange", "env2-box-arrange.py")
    env2_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(env2_module)
    run_exp = env2_module.run_exp
    
    print("run_exp function imported successfully")
    print("Calling run_exp...")
    
    user_prompt_list, response_total_list, pg_state_list, success_failure, index_query_times, token_num_count_list, Saving_path_result = run_exp(
        Saving_path, pg_row_num, pg_column_num, iteration_num, query_time_limit, 
        dialogue_history_method='_w_only_state_action_history',
        cen_decen_framework=cen_decen_framework, 
        local_model_choice=local_model_choice
    )
    
    print("run_exp completed successfully!")
    print(f"Success/Failure: {success_failure}")
    print(f"Query times: {index_query_times}")
    
except Exception as e:
    print(f"run_exp failed with error: {e}")
    import traceback
    traceback.print_exc()

print("Test completed!")
