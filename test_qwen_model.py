#!/usr/bin/env python3

from LLM import *

def test_qwen_model():
    """Test the qwen3-235b-a22b model specifically"""
    print("Testing qwen3-235b-a22b model...")
    
    # Simple test message
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Say hello"}
    ]
    
    # Test with the qwen model that HMAS-2 central planner uses
    model_name = 'qwen3-235b-a22b'
    
    try:
        print(f"Calling GPT_response with model: {model_name}")
        response, token_count = GPT_response(messages, model_name)
        print(f"Response: {response}")
        print(f"Token count: {token_count}")
        print("API call successful!")
        return True
    except Exception as e:
        print(f"API call failed with error: {e}")
        return False

if __name__ == "__main__":
    success = test_qwen_model()
    if success:
        print("✓ Qwen model test passed")
    else:
        print("✗ Qwen model test failed")
