#!/usr/bin/env python3

from LLM import *

def test_api_call():
    """Test the API call with timeout to see if it works properly"""
    print("Testing API call with timeout...")
    
    # Simple test message
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Say hello"}
    ]
    
    # Test with the gemma model
    model_name = 'gemma-3-27b'
    
    try:
        print(f"Calling GPT_response with model: {model_name}")
        response, token_count = GPT_response(messages, model_name)
        print(f"Response: {response}")
        print(f"Token count: {token_count}")
        print("API call successful!")
        return True
    except Exception as e:
        print(f"API call failed with error: {e}")
        return False

if __name__ == "__main__":
    success = test_api_call()
    if success:
        print("✓ API test passed")
    else:
        print("✗ API test failed")
