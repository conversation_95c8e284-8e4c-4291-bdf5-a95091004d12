#!/usr/bin/env python3

from LLM import *
from prompt_env2 import *
from env2_create import *
from sre_constants import error
import random
import os
import json
import re
import copy
import numpy as np
import shutil
import time

print("All imports successful!")

# Configuration for model selection
cen_decen_framework = 'HMAS-2'  # Options: 'CMAS', 'DMAS', 'HMAS-1', 'HMAS-2'
local_model_choice = 'gemma-3-27b'  # Options: 'gemma-3-27b', 'qwen3-30b-a3b-mlx'

print(f'-------------------Framework: {cen_decen_framework}, Local Model: {local_model_choice}-------------------')

# Test the first iteration only
pg_row_num, pg_column_num = 2, 2
iteration_num = 0
query_time_limit = 30

print('-------###-------###-------###-------')
print(f'Row num is: {pg_row_num}, Column num is: {pg_column_num}, Iteration num is: {iteration_num}\n\n')
print(f'query_time_limit: {query_time_limit}')

# Test path creation
Code_dir_path = 'path_to_multi-agent-framework/multi-agent-framework/' # Put the current code directory path here
Saving_path = Code_dir_path + 'Env2_WorkpieceNet2'

print(f"Saving path: {Saving_path}")

# Test if we can create the directory structure
Saving_path_result = Saving_path+f'/env_pg_state_{pg_row_num}_{pg_column_num}/pg_state{iteration_num}/{cen_decen_framework}_w_only_state_action_history'

print(f"Saving path result: {Saving_path_result}")

try:
    os.makedirs(Saving_path_result, exist_ok=True)
    print("Directory creation successful!")
except Exception as e:
    print(f"Directory creation failed: {e}")

# Test loading the JSON file
json_file_path = Saving_path+f'/env_pg_state_{pg_row_num}_{pg_column_num}/pg_state{iteration_num}/pg_state{iteration_num}.json'
print(f"Loading JSON file: {json_file_path}")

try:
    with open(json_file_path, 'r') as f:
        pg_dict = json.load(f)
    print("JSON file loaded successfully!")
    print(f"Keys in pg_dict: {list(pg_dict.keys())}")
except Exception as e:
    print(f"JSON file loading failed: {e}")
    exit(1)

# Test model selection
model_name = get_model_for_framework(cen_decen_framework, 'central', local_model_choice)
print(f"Selected model: {model_name}")

# Test creating a simple prompt
print("Creating test prompt...")
try:
    # Create a simple test prompt
    test_messages = [
        {"role": "system", "content": "You are a helpful assistant for multi-agent coordination."},
        {"role": "user", "content": "Please respond with a simple JSON object: {\"status\": \"ready\"}"}
    ]
    
    print("Making test API call...")
    response, token_count = GPT_response(test_messages, model_name)
    print(f"Test API call successful!")
    print(f"Response: {response}")
    print(f"Token count: {token_count}")
    
except Exception as e:
    print(f"Test API call failed: {e}")

print("Step-by-step test completed!")
